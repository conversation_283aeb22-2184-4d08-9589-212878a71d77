package com.example.pdf.lyricist.i18n

import cafe.adriel.lyricist.LyricistStrings
import com.example.pdf.lyricist.Locales
import com.example.pdf.lyricist.Strings

@LyricistStrings(languageTag = Locales.EN, default = true)
val StringsEn = Strings(
  // Common Actions
  textOK = "OK",
  textCancel = "Cancel",
  textSave = "Save",
  textDelete = "Delete",
  textShare = "Share",
  textOpen = "Open",
  textConvert = "Convert",
  textGrant = "Grant",
  textAccept = "Accept",
  textDecline = "Decline",
  textSettings = "Settings",
  textFeedback = "Feedback",
  textPrivacyPolicy = "Privacy Policy",
  textVersion = "Version",

  // Navigation Tabs
  tabAll = "All",
  tabRecent = "Recent",
  tabBookmarks = "Bookmarks",
  tabTools = "Tools",

  // Tool Names
  toolScanToPdf = "Scan to PDF",
  toolLockPdf = "Lock PDF",
  toolUnlockPdf = "Unlock PDF",
  toolAnnotate = "Annotate",
  toolSignature = "Signature",
  toolImportFile = "Import File",
  toolWordToPdf = "Word to PDF",
  toolImagesToPdf = "Images to PDF",

  // Status Messages
  statusLoading = "Loading...",
  statusConvertedSuccessfully = "Converted successfully",
  statusScannedSuccessfully = "Scanned successfully",
  statusLockedSuccessfully = "Locked successfully",
  statusUnlockedSuccessfully = "Unlocked successfully",

  // Placeholder Text
  placeholderSearch = "Search...",
  placeholderNoFilesFound = "No files found",
  placeholderNoBookmarks = "You haven't added any bookmarks yet.",

  // Dialog Titles
  dialogPermissionRequired = "Permission Required",
  dialogTermsConditions = "Terms & Conditions",
  dialogSimpleAlert = "Simple Alert",

  // Permission Messages
  permissionFileAccessTips = "To read and edit files, we need All Files Access permission. This will open system settings to allow permission.",
  permissionFileAccessTipsShort = "Permission is required to access all files",
  permissionFileAccessTipsLegacy = "To read and edit files, we need storage access permission.",

  // Content Descriptions
  contentDescriptionSearch = "Search",
  contentDescriptionBookmarks = "Bookmarks",
  contentDescriptionTools = "Tools",
  contentDescriptionNoFiles = "no files found",
  contentDescriptionNoBookmarks = "no bookmarks",
  contentDescriptionAppIcon = "App Icon",
  contentDescriptionOpen = "Open",
  contentDescriptionShare = "Share",
  contentDescriptionDismissTips = "dismiss tips",

  // Convert Actions
  convertToPdf = "Convert to PDF",
  convertWithCount = "Convert",

  // Long Content Example
  longContentExample = "This is a longer content example that demonstrates how the alert dialog handles multiple lines of text. The content will automatically adjust to show longer messages while maintaining readability. This is particularly useful for displaying terms and conditions or detailed information to users.",

  // Rating and Feedback
  feedbackDialogMessage = "We are here to help and make things better for you.",
  skipForNow = "Skip for now",
  renamedSuccessfully = "Renamed successfully",

  // Password Dialogs
  setPassword = "Set password",
  removePassword = "Remove password",
  enterPassword = "Enter password",
  passwordPlaceholder = "Password",
  createPasswordMessage = "Create a password to protect your PDF.",
  enterPasswordMessage = "Enter the password to unlock this PDF.",
  removePasswordMessage = "Password protection will be removed from the PDF.",

  // Password Success/Error Messages
  passwordSetSuccessfully = "Password set successfully",
  passwordRemovedSuccessfully = "Password removed successfully",
  passwordCannotBeEmpty = "Password cannot be empty",
  failedToSetPassword = "Failed to set password",
  incorrectPasswordOrFailed = "Incorrect password or failed to remove password",
  errorPrefix = "Error",

  // PDF Toolbar Menu Items
  pdfDownload = "Download",
  pdfZoom = "Zoom",
  pdfGoToPage = "Go to page",
  pdfRotateClockwise = "Rotate Clockwise",
  pdfRotateAntiClockwise = "Rotate Anti Clockwise",
  pdfScrollMode = "Scroll Mode",
  pdfCustomPageArrangement = "Custom Page Arrangement",
  pdfSpreadMode = "Split Mode",
  pdfAlignMode = "Align Mode",
  pdfSnapPage = "Snap Page",
  pdfProperties = "Properties",

  // PDF Dialog Titles
  selectPageScrollMode = "Select Page Scroll Mode",
  selectPageAlignMode = "Select Page Align Mode",
  documentProperties = "Document Properties",
  textClose = "Close",
  text = "Text",
  fontSize = "Font Size",

  // PDF Properties
  fileName = "File Name",
  fileSize = "File Size",
  title = "Title",
  subject = "Subject",
  author = "Author",
  creator = "Creator",
  producer = "Producer",
  creationDate = "Creation Date",
  modifiedDate = "Modified Date",
  keywords = "Keywords",
  pdfVersion = "Pdf Version",
  fastWebview = "Fast Webview",

  // XML Layout Strings
  home = "Home",
  scan = "Scan",

  // Empty State Messages
  noFilesFoundWithType = "No %s files found",

  // Search and Input Placeholders
  placeholderSearchInDocument = "Search in document...",

  // Password Dialog Strings
  enterPasswordTitle = "Enter Password",
  passwordProtectedMessage = "This PDF is password protected. Please enter the password to open it.",
  unlockButton = "Unlock",

  // Loading Messages
  loadingWithSpaces = " Loading...",

  // Document Picker
  selectDocumentUri = "Select Document (Uri)",

  // PDF Toolbar Actions
  textDone = "Done",
  textGo = "Go",
  textUnlock = "Unlock",
  textFind = "Find",
  textEdit = "Edit",
  textHighlight = "Highlight",
  textText = "Text",
  textDraw = "Draw",
  textAddEditImages = "Add/Edit Images",
  textShowAll = "Show All",
  textEnable = "Enable",
  textThickness = "Thickness",
  textFontSize = "Font Size",
  textOpacity = "Opacity",
  textPageNumber = "Page Number",

  // PDF Dialog Titles
  selectZoomLevel = "Select Zoom Level",
  goToPage = "Go to page",
  selectPageScrollMode = "Select Page Scroll Mode",
  singlePageArrangement = "Single Page Arrangement",
  selectPageSplitMode = "Select Page Split Mode",
  selectPageAlignMode = "Select Page Align Mode",
  snapPage = "Snap Page",
  documentProperties = "Document Properties",
  highlightColor = "Highlight Color",

  // PDF Zoom Options
  zoomAutomatic = "Automatic",
  zoomPageFit = "Page Fit",
  zoomPageWidth = "Page Width",
  zoomActualSize = "Actual Size",
  zoom50Percent = "50%",
  zoom75Percent = "75%",
  zoom100Percent = "100%",
  zoom125Percent = "125%",
  zoom150Percent = "150%",
  zoom200Percent = "200%",
  zoom300Percent = "300%",
  zoom400Percent = "400%",

  // PDF Scroll Mode Options
  scrollModeVertical = "Vertical",
  scrollModeHorizontal = "Horizontal",
  scrollModeWrapped = "Wrapped",
  scrollModeSinglePage = "Single Page",

  // PDF Split Mode Options
  splitModeNone = "None",
  splitModeOdd = "Odd",
  splitModeEven = "Even",

  // PDF Align Mode Options
  alignModeDefault = "Default",
  alignModeCenterVertically = "Center Vertically",
  alignModeCenterHorizontally = "Center Horizontally",
  alignModeCenterBoth = "Center Both",

  // PDF Properties Labels
  propertyFileName = "File Name",
  propertyFileSize = "File Size",
  propertyTitle = "Title",
  propertySubject = "Subject",
  propertyAuthor = "Author",
  propertyCreator = "Creator",
  propertyProducer = "Producer",
  propertyCreationDate = "Creation Date",
  propertyModifiedDate = "Modified Date",
  propertyKeywords = "Keywords",
  propertyPdfVersion = "Pdf Version",
  propertyFastWebview = "Fast Webview",

  // Toast Messages
  toastNoMatchFound = "No match found!",

  // Misc
  dialogDescription = "Dialog",
  sampleTool = "Sample Tool",
  longPressDragReorder = "Long press and drag to reorder",
  importDocument = "Import Document",
  selectFile = "Select File"
)