package com.example.pdf.lyricist.i18n

import cafe.adriel.lyricist.LyricistStrings
import com.example.pdf.lyricist.Locales
import com.example.pdf.lyricist.Strings

@LyricistStrings(languageTag = Locales.EN, default = true)
val StringsEn = Strings(
  // Common Actions
  textOK = "OK",
  textCancel = "Cancel",
  textSave = "Save",
  textDelete = "Delete",
  textShare = "Share",
  textOpen = "Open",
  textConvert = "Convert",
  textGrant = "Grant",
  textAccept = "Accept",
  textDecline = "Decline",
  textSettings = "Settings",
  textFeedback = "Feedback",
  textPrivacyPolicy = "Privacy Policy",
  textVersion = "Version",

  // Navigation Tabs
  tabAll = "All",
  tabRecent = "Recent",
  tabBookmarks = "Bookmarks",
  tabTools = "Tools",

  // Tool Names
  toolScanToPdf = "Scan to PDF",
  toolLockPdf = "Lock PDF",
  toolUnlockPdf = "Unlock PDF",
  toolAnnotate = "Annotate",
  toolSignature = "Signature",
  toolImportFile = "Import File",
  toolWordToPdf = "Word to PDF",
  toolImagesToPdf = "Images to PDF",

  // Status Messages
  statusLoading = "Loading...",
  statusConvertedSuccessfully = "Converted successfully",
  statusScannedSuccessfully = "Scanned successfully",
  statusLockedSuccessfully = "Locked successfully",
  statusUnlockedSuccessfully = "Unlocked successfully",

  // Placeholder Text
  placeholderSearch = "Search...",
  placeholderNoFilesFound = "No files found",
  placeholderNoBookmarks = "You haven't added any bookmarks yet.",

  // Dialog Titles
  dialogPermissionRequired = "Permission Required",
  dialogTermsConditions = "Terms & Conditions",
  dialogSimpleAlert = "Simple Alert",

  // Permission Messages
  permissionFileAccessTips = "To read and edit files, we need All Files Access permission. This will open system settings to allow permission.",
  permissionFileAccessTipsShort = "Permission is required to access all files",
  permissionFileAccessTipsLegacy = "To read and edit files, we need storage access permission.",

  // Content Descriptions
  contentDescriptionSearch = "Search",
  contentDescriptionBookmarks = "Bookmarks",
  contentDescriptionTools = "Tools",
  contentDescriptionNoFiles = "no files found",
  contentDescriptionNoBookmarks = "no bookmarks",
  contentDescriptionAppIcon = "App Icon",
  contentDescriptionOpen = "Open",
  contentDescriptionShare = "Share",
  contentDescriptionDismissTips = "dismiss tips",

  // Convert Actions
  convertToPdf = "Convert to PDF",
  convertWithCount = "Convert",

  // Long Content Example
  longContentExample = "This is a longer content example that demonstrates how the alert dialog handles multiple lines of text. The content will automatically adjust to show longer messages while maintaining readability. This is particularly useful for displaying terms and conditions or detailed information to users.",

  // Rating and Feedback
  feedbackDialogMessage = "We are here to help and make things better for you.",
  skipForNow = "Skip for now",
  renamedSuccessfully = "Renamed successfully",

  // Password Dialogs
  setPassword = "Set password",
  removePassword = "Remove password",
  enterPassword = "Enter password",
  passwordPlaceholder = "Password",
  createPasswordMessage = "Create a password to protect your PDF.",
  enterPasswordMessage = "Enter the password to unlock this PDF.",

  // Misc
  dialogDescription = "Dialog",
  sampleTool = "Sample Tool",
  longPressDragReorder = "Long press and drag to reorder",
  importDocument = "Import Document",
  selectFile = "Select File"
)