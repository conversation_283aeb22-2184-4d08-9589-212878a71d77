package com.example.pdf.ui.node.document.pdf

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material.icons.rounded.Clear
import androidx.compose.material.icons.rounded.Visibility
import androidx.compose.material.icons.rounded.VisibilityOff
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.LifecycleStartEffect
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.BasicAlertDialog
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.ButtonVariant
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.lumo.components.textfield.TextField
import com.example.pdf.lumo.components.textfield.TextFieldDefaults
import com.example.pdf.ui.composable.BlankSpacer

/**
 * A composable that displays a password dialog for PDF files.
 * This version requires all state to be managed externally.
 *
 * @param password The current password value
 * @param passwordVisible Whether the password is currently visible
 * @param errorMessage Error message to display, or null if no error
 * @param onPasswordChange Callback when password changes
 * @param onTogglePasswordVisibility Callback to toggle password visibility
 * @param onPasswordClear Callback to clear the password
 * @param onConfirm Callback when user confirms the password
 * @param onCancel Callback when user cancels
 * @param onDismissRequest Callback when dialog is dismissed by clicking outside or back button
 */
@Composable
fun PdfViewerPasswordDialog(
  password: TextFieldValue,
  passwordVisible: Boolean,
  errorMessage: String?,
  onPasswordChange: (TextFieldValue) -> Unit,
  onTogglePasswordVisibility: () -> Unit,
  onPasswordClear: () -> Unit,
  onConfirm: (String) -> Unit,
  onCancel: () -> Unit,
  onDismissRequest: () -> Unit,
  modifier: Modifier = Modifier
) {
  BasicAlertDialog(
    onDismissRequest = onDismissRequest,
    properties = DialogProperties(
      dismissOnBackPress = true,
      dismissOnClickOutside = true
    )
  ) {
    PdfViewerPasswordDialogContent(
      password = password,
      passwordVisible = passwordVisible,
      errorMessage = errorMessage,
      onPasswordChange = onPasswordChange,
      onTogglePasswordVisibility = onTogglePasswordVisibility,
      onPasswordClear = onPasswordClear,
      onConfirm = onConfirm,
      onCancel = onCancel,
      modifier = modifier
    )
  }
}

/**
 * A simplified version of PdfViewerPasswordDialog that manages state internally.
 * Only requires onConfirm and onCancel callbacks.
 *
 * @param onConfirm Callback when user confirms the password
 * @param onCancel Callback when user cancels
 * @param onDismissRequest Callback when dialog is dismissed by clicking outside or back button
 */
@Composable
fun PdfViewerPasswordDialog(
  onConfirm: (String) -> Unit,
  onCancel: () -> Unit,
  onDismissRequest: () -> Unit,
  modifier: Modifier = Modifier,
  isFirstTimeInput: Boolean = true
) {
  var password by remember { mutableStateOf(TextFieldValue()) }
  var passwordVisible by remember { mutableStateOf(false) }
  var errorMessage by remember { mutableStateOf<String?>(null) }

  LaunchedEffect(isFirstTimeInput) {
    if (isFirstTimeInput == false) {
      errorMessage = "Incorrect password"
    }
  }

  BasicAlertDialog(
    onDismissRequest = onDismissRequest,
  ) {
    PdfViewerPasswordDialogContent(
      password = password,
      passwordVisible = passwordVisible,
      errorMessage = errorMessage,
      onPasswordChange = {
        password = it
        errorMessage = null
      },
      onTogglePasswordVisibility = { passwordVisible = !passwordVisible },
      onPasswordClear = { password = TextFieldValue() },
      onConfirm = { passwordText ->
        if (passwordText.isNotEmpty()) {
          onConfirm(passwordText)
        } else {
          errorMessage = "Password cannot be empty"
        }
      },
      onCancel = onCancel,
      modifier = modifier,
    )
  }
}

/**
 * The content of the PDF viewer password dialog.
 * This is an internal implementation used by both public PdfViewerPasswordDialog functions.
 */
@Composable
private fun PdfViewerPasswordDialogContent(
  password: TextFieldValue,
  passwordVisible: Boolean,
  errorMessage: String?,
  onPasswordChange: (TextFieldValue) -> Unit,
  onTogglePasswordVisibility: () -> Unit,
  onPasswordClear: () -> Unit,
  onConfirm: (String) -> Unit,
  onCancel: () -> Unit,
  modifier: Modifier = Modifier,
) {
  val focusRequester = FocusRequester()
  val keyboardController = LocalSoftwareKeyboardController.current
  val focusManager = LocalFocusManager.current

  LifecycleStartEffect(Unit) {
    onStopOrDispose {
      focusManager.clearFocus()
      keyboardController?.hide()
    }
  }

  LaunchedEffect(Unit) {
    focusRequester.requestFocus()
  }

  Column(
    modifier = modifier
      .background(color = AppTheme.colors.surface, shape = CardDefaults.Shape)
      .clip(CardDefaults.Shape)
      .fillMaxWidth()
      .padding(24.dp)
      .imePadding(),
    horizontalAlignment = Alignment.CenterHorizontally
  ) {
    Text(text = LocalStrings.current.enterPasswordTitle, style = AppTheme.typography.h2)

    Spacer(modifier = Modifier.height(16.dp))

    Text(
      text = LocalStrings.current.passwordProtectedMessage,
      style = AppTheme.typography.body1,
      color = AppTheme.colors.onBackground.copy(alpha = 0.7f),
      textAlign = TextAlign.Center
    )

    BlankSpacer(8.dp)

    TextField(
      value = password,
      onValueChange = onPasswordChange,
      colors = TextFieldDefaults.colors().copy(
        unfocusedContainerColor = AppTheme.colors.secondary.copy(.25f),
        focusedContainerColor = AppTheme.colors.secondary.copy(.25f),
        cursorColor = AppTheme.colors.primary,
      ),
      modifier = Modifier
        .padding(vertical = 24.dp)
        .fillMaxWidth()
        .focusRequester(focusRequester),
      placeholder = { Text(LocalStrings.current.passwordPlaceholder) },
      visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
      trailingIcon = {
        Row {
          if (password.text.isNotEmpty()) {
            IconButton(
              onClick = onPasswordClear,
              variant = IconButtonVariant.Secondary,
              shape = CircleShape,
              modifier = Modifier.size(22.dp)
            ) {
              Icon(imageVector = Icons.Rounded.Clear)
            }

            BlankSpacer(6.dp)
          }

          IconButton(
            onClick = onTogglePasswordVisibility,
            variant = IconButtonVariant.Secondary,
            shape = CircleShape,
            modifier = Modifier.size(22.dp)
          ) {
            Icon(imageVector = if (passwordVisible) Icons.Rounded.VisibilityOff else Icons.Rounded.Visibility)
          }
        }
      },
      isError = errorMessage != null,
      supportingText = errorMessage?.let { { Text(text = it) } }
    )

    Row(modifier = Modifier.fillMaxWidth()) {
      Button(
        variant = ButtonVariant.Secondary,
        text = LocalStrings.current.textCancel,
        onClick = { onCancel() },
        modifier = Modifier.weight(1f)
      )

      Spacer(modifier = Modifier.size(16.dp))

      Button(
        variant = ButtonVariant.Primary,
        text = LocalStrings.current.unlockButton,
        onClick = {
          val passwordText = password.text.trim()
          if (passwordText.isNotEmpty()) {
            onConfirm(passwordText)
          } else {
            // 这里不直接调用 onConfirm，而是让调用者处理错误
            // 在简化版本中，我们在外层函数中处理这个逻辑
          }
        },
        modifier = Modifier.weight(1f)
      )
    }
  }
}
