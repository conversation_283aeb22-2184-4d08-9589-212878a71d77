package com.example.pdf.ui.node.word2pdf

import android.net.Uri
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.bhuvaneshw.pdf.compose.rememberPdfState
import com.bhuvaneshw.pdf.compose.ui.PdfScrollBar
import com.bhuvaneshw.pdf.compose.ui.PdfViewer
import com.bhuvaneshw.pdf.compose.ui.PdfViewerContainer
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.OnTryToShowInterAdAndNavAction
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Surface
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.lumo.components.topbar.TopBarDefaults
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.loading.LoadingDialog
import com.example.pdf.ui.feature.loading.LoadingPanel
import com.roudikk.guia.core.Navigator
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState

@Parcelize
class WordToPdfNode(
  private val wordUri: Uri
) : ScreenNode("word_to_pdf") {
  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    val uiModel: WordToPdfUiModel = koinUiModel { parametersOf(wordUri) }
    val uiState by uiModel.collectAsState()

    val (onTryToShowInterAdAndNavAction, onBackAction)
      = interstitialAdRegister(navigator)

    WordToPdfContent(
      uiState = uiState,
      onBack = onBackAction,
      onConvert = {
        onTryToShowInterAdAndNavAction {
          uiModel.onConvert()
        }
      },
    )
  }
}

@Composable
private fun WordToPdfContent(
  uiState: WordToPdfUiState,
  onBack: () -> Unit,
  onConvert: () -> Unit,
) {
  if (uiState.converting) {
    LoadingDialog()
  }

  Scaffold(
    topBar = {
      TopBar(
        colors = TopBarDefaults.topBarColors(containerColor = AppTheme.colors.surface)
      ) {
        Row(
          modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp)
        ) {
          IconButton(onClick = onBack, shape = CircleShape, variant = IconButtonVariant.Ghost) {
            Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
          }
        }
      }
    },
    bottomBar = {
      Surface(modifier = Modifier.navigationBarsPadding()) {
        Column {
          Button(
            onClick = onConvert,
            enabled = uiState.isLoadingPreview.not() && uiState.previewPdf != null,
            modifier = Modifier
              .fillMaxWidth()
              .padding(horizontal = 24.dp, vertical = 18.dp)
          ) {
            Text(
              text = LocalStrings.current.convertToPdf,
              style = AppTheme.typography.buttonLarge,
              modifier = Modifier.padding(vertical = 8.dp)
            )
          }

          BannerAd(BannerAdPlace.Word2Pdf)
        }
      }
    }
  ) {
    Crossfade(uiState.isLoadingPreview.not() && uiState.previewPdf != null) { previewReady ->
      if (previewReady) {
        requireNotNull(uiState.previewPdf)
        val pdfState = rememberPdfState(source = uiState.previewPdf.absolutePath)

        PdfViewerContainer(
          pdfState = pdfState,
          pdfViewer = {
            PdfViewer()
          },
          pdfToolBar = null,
          pdfScrollBar = { parentSize ->
            PdfScrollBar(
              parentSize = parentSize
            )
          },
          modifier = Modifier
            .fillMaxSize()
            .padding(it)
        )
      } else {
        LoadingPanel(
          modifier = Modifier
            .background(AppTheme.colors.disabled.copy(.15f))
            .fillMaxSize()
            .padding(it),
        )
      }
    }
  }
}