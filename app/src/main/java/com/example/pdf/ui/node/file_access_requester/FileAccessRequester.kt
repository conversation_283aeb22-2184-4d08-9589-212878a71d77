package com.example.pdf.ui.node.file_access_requester

import android.Manifest
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.android.permission.FileAccessManager
import com.example.pdf.biz.skipSplash
import com.example.pdf.guia.BottomSheetNode
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.ui.feature.bottomsheet.permission.PermissionBottomSheet
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import com.roudikk.guia.core.BottomSheet
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject

@Parcelize
class FileAccessRequesterNode : BottomSheetNode(tag = "file_access_requester") {
  @Composable
  override fun Content(navigator: Navigator, bottomSheet: BottomSheet?) {
    val fileAccessManager: FileAccessManager = koinInject()

    val fileAccessPermissionLauncher = rememberLauncherForActivityResult(
      ActivityResultContracts.StartActivityForResult()
    ) { _ ->
      if (fileAccessManager.hasFileAccessPermission()) {
        // Permission granted, dismiss the bottom sheet
        navigator.pop()
      }
    }

    val externalStoragePermissionState = rememberMultiplePermissionsState(
      listOf(
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
      )
    ) {
      if (it.values.all { isGranted -> isGranted }) {
        // All permissions are granted, dismiss the bottom sheet
        navigator.pop()
      }
    }

    FileAccessRequesterContent(
      onGrantClick = {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
          // For Android 11+ (API 30+), we need to use MANAGE_EXTERNAL_STORAGE
          val intent = fileAccessManager.createFileAccessPermissionIntent()
          if (intent != null) {
            skipSplash()
            fileAccessPermissionLauncher.launch(intent)
          }
        } else {
          // For Android 10 and below, use EXTERNAL_STORAGE
          externalStoragePermissionState.launchMultiplePermissionRequest()
        }
      }
    )
  }
}

@Composable
private fun FileAccessRequesterContent(
  onGrantClick: () -> Unit = {}
) {
  val strings = LocalStrings.current

  PermissionBottomSheet(
    iconPainter = painterResource(R.drawable.ic_permission_access_file_locked_c),
    title = strings.dialogPermissionRequired,
    description = strings.permissionFileAccessTips,
    buttonText = strings.textGrant,
    onButtonClick = onGrantClick
  )
}


@Preview
@Composable
private fun FileAccessRequesterContentPreview() {
  PreviewComposable {
    FileAccessRequesterContent()
  }
}
