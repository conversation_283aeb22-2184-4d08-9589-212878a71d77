package com.example.pdf.ui.node.pdf_picker

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleStartEffect
import com.example.pdf.android.res.ResDrawable
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.HorizontalDivider
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.lumo.components.textfield.TextField
import com.example.pdf.lumo.components.textfield.TextFieldDefaults
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.feature.document_item.DocumentItem
import com.google.common.collect.Multimaps.index
import java.io.File

@Composable
fun PdfPickerContent(
  uiState: PdfPickerUiState,
  onQueryChange: (TextFieldValue) -> Unit,
  onQueryClear: () -> Unit,
  onListTypeChange: (PdfPickerListType) -> Unit,
  onFileClick: (File) -> Unit,
  onBookmarkClick: (File, Boolean) -> Unit,
  onBack: () -> Unit
) {
  Scaffold(
    topBar = {
      PdfPickerTopBar(
        query = uiState.query,
        onQueryChange = onQueryChange,
        onQueryClear = onQueryClear,
        onBack = onBack
      )
    },
    bottomBar = {
      BannerAd(BannerAdPlace.PdfPicker, modifier = Modifier.navigationBarsPadding())
    }
  ) { paddingValues ->
    Column(
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues)
    ) {
      // Filter chips for PDF types
//      PdfTypeFilterChips(
//        selectedType = uiState.listType,
//        onTypeSelected = onListTypeChange,
//        modifier = Modifier
//          .fillMaxWidth()
//          .padding(horizontal = 16.dp, vertical = 8.dp)
//      )

      // File count
      Text(
        text = "${uiState.filteredPdfFiles.size} item(s)",
        style = AppTheme.typography.body2,
        color = AppTheme.colors.onBackground.copy(.7f),
        fontWeight = FontWeight.Medium,
        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
      )

      // PDF file list
      LazyColumn(
        modifier = Modifier.fillMaxSize()
      ) {
        uiState.filteredPdfFiles.forEachIndexed { index, pdfFile ->
          item {
            val isBookmarked = uiState.bookmarkedFilePaths.contains(pdfFile.absolutePath)

            DocumentItem(
              file = pdfFile,
              isShowMoreIcon = false,
              isBookmarked = isBookmarked,
              isShowBookmarkIcon = isBookmarked,
//            onBookmarkClick = onBookmarkClick,
              onClick = { onFileClick(pdfFile) },
              modifier = Modifier.animateItem()
            )
          }

          if (index == minOf(2, uiState.filteredPdfFiles.size - 1)) {
            item("AD_ITEM") {
              NativeAd(adPlace = NativeAdPlace.PdfPicker)
            }
          }
        }
      }
    }
  }
}

@Composable
private fun PdfPickerTopBar(
  query: TextFieldValue,
  onQueryChange: (TextFieldValue) -> Unit,
  onQueryClear: () -> Unit,
  onBack: () -> Unit
) {
  Column {
    TopAppBar(
      title = { Text(text = "Select a file", style = AppTheme.typography.h2) },
      navigationIcon = {
        IconButton(onClick = onBack, shape = CircleShape, variant = IconButtonVariant.Ghost) {
          Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
        }
      },
      colors = TopAppBarDefaults.topAppBarColors(containerColor = AppTheme.colors.surface),
    )

    HorizontalDivider()

    PdfSearchBar(
      query = query,
      onQueryChange = onQueryChange,
      onQueryClear = onQueryClear,
      modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
    )
  }
}

@Composable
private fun PdfSearchBar(
  query: TextFieldValue,
  onQueryChange: (TextFieldValue) -> Unit,
  onQueryClear: () -> Unit,
  modifier: Modifier = Modifier,
) {
  val focusRequester = remember { FocusRequester() }
  val keyboardController = LocalSoftwareKeyboardController.current
  val focusManager = LocalFocusManager.current

  LifecycleStartEffect(Unit) {
//    focusRequester.requestFocus()

    onStopOrDispose {
      focusManager.clearFocus()
      keyboardController?.hide()
    }
  }

  TextField(
    modifier = modifier.focusRequester(focusRequester),
    value = query,
    onValueChange = onQueryChange,
    singleLine = true,
    shape = CardDefaults.Shape,
    colors = TextFieldDefaults.colors().copy(
      unfocusedContainerColor = AppTheme.colors.secondary.copy(.25f),
      focusedContainerColor = AppTheme.colors.secondary.copy(.25f),
      cursorColor = AppTheme.colors.primary,
    ),
    placeholder = { Text(LocalStrings.current.placeholderSearch) },
    leadingIcon = {
      Icon(
        painter = painterResource(ResDrawable.ic_search),
        contentDescription = LocalStrings.current.contentDescriptionSearch,
        tint = AppTheme.colors.onSurface.copy(.8f)
      )
    },
    trailingIcon = {
      if (query.text.isNotEmpty()) {
        IconButton(
          onClick = onQueryClear,
          variant = IconButtonVariant.Secondary,
          shape = CircleShape,
          modifier = Modifier.size(24.dp)
        ) {
          Icon(imageVector = Icons.Filled.Clear)
        }
      }
    },
  )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PdfTypeFilterChips(
  selectedType: PdfPickerListType,
  onTypeSelected: (PdfPickerListType) -> Unit,
  modifier: Modifier = Modifier
) {
  Row(
    modifier = modifier,
    horizontalArrangement = Arrangement.spacedBy(8.dp)
  ) {
    FilterChip(
      selected = selectedType == PdfPickerListType.ALL,
      onClick = { onTypeSelected(PdfPickerListType.ALL) },
      label = { Text("All") },
      colors = FilterChipDefaults.filterChipColors(
        selectedContainerColor = AppTheme.colors.primary,
        selectedLabelColor = AppTheme.colors.onPrimary
      )
    )

    FilterChip(
      selected = selectedType == PdfPickerListType.Encrypted,
      onClick = { onTypeSelected(PdfPickerListType.Encrypted) },
      label = { Text("Encrypted") },
      colors = FilterChipDefaults.filterChipColors(
        selectedContainerColor = AppTheme.colors.primary,
        selectedLabelColor = AppTheme.colors.onPrimary
      )
    )

    FilterChip(
      selected = selectedType == PdfPickerListType.Unencrypted,
      onClick = { onTypeSelected(PdfPickerListType.Unencrypted) },
      label = { Text("Unencrypted") },
      colors = FilterChipDefaults.filterChipColors(
        selectedContainerColor = AppTheme.colors.primary,
        selectedLabelColor = AppTheme.colors.onPrimary
      )
    )
  }
}


