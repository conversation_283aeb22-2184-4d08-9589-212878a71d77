package com.example.pdf.ui.node.pdf_remove_password

import android.app.Application
import androidx.compose.ui.text.input.TextFieldValue
import com.example.pdf.android.pdf.PdfBoxInitializer
import com.example.pdf.android.toast.showToast
import com.example.pdf.biz.ad.interstitial.OnTryToShowInterAdAndNavAction
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.kermit.debugLog
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.node.convert_success.UnlockPdfSuccessNode
import com.example.pdf.ui.node.pdf_picker.PdfPasswordAction
import com.example.pdf.ui.node.pdf_picker.PdfPasswordActionEventFlow
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.replaceLast
import org.koin.android.annotation.KoinViewModel
import java.io.File
import com.tom_roush.pdfbox.pdmodel.PDDocument

data class PdfRemovePasswordUiState(
  val isLoading: Boolean = false,
  val password: TextFieldValue = TextFieldValue(),
  val passwordVisible: Boolean = false,
  val errorMessage: String? = null
)

@KoinViewModel
class PdfRemovePasswordUiModel(
  private val pdfFile: File,
  private val application: Application
) : UiModel<PdfRemovePasswordUiState, Nothing>(PdfRemovePasswordUiState()) {

  fun onPasswordChange(password: TextFieldValue) = intent {
    reduce { state.copy(password = password, errorMessage = null) }
  }

  fun onTogglePasswordVisibility() = intent {
    reduce { state.copy(passwordVisible = !state.passwordVisible) }
  }

  fun onPasswordClear() = intent {
    reduce { state.copy(password = TextFieldValue(), errorMessage = null) }
  }

  fun onCancel() = intent {
    GlobalNavigator.transaction { pop() }
  }

  fun onConfirm(
    onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction
  ) = intent {
    val password = state.password.text.trim()

    if (password.isEmpty()) {
      reduce { state.copy(errorMessage = "Password cannot be empty") }
      return@intent
    }

    reduce { state.copy(isLoading = true, errorMessage = null) }

    try {
      val success = removePasswordFromPdf(pdfFile, password)

      if (success) {
        PdfPasswordActionEventFlow.emit(PdfPasswordAction.RemovePassword(pdfFile.absolutePath))
        onTryToShowInterAdAndNavAction {
          showToast(globalStrings.passwordRemovedSuccessfully)

          pop()
          replaceLast(UnlockPdfSuccessNode(pdfFile))
        }
      } else {
        reduce {
          state.copy(
            isLoading = false,
            errorMessage = globalStrings.incorrectPasswordOrFailed
          )
        }
      }
    } catch (e: Exception) {
      debugLog(
        tag = "PdfRemovePasswordUiModel",
        throwable = e
      ) { "Error removing password: ${e.message}" }
      reduce { state.copy(isLoading = false, errorMessage = "${globalStrings.errorPrefix}: ${e.message}") }
    }
  }

  private suspend fun removePasswordFromPdf(pdfFile: File, password: String): Boolean {
    return PdfBoxInitializer.ensureInitialized(application) {
      try {
        // Load the PDF document with the password
        val document = PDDocument.load(pdfFile, password)

        // If the document is encrypted, remove the encryption
        if (document.isEncrypted) {
          // Create a temporary file to save the unencrypted document
          val tempFile = File(pdfFile.parent, "temp_${pdfFile.name}")

          // Save the document without encryption
          document.setAllSecurityToBeRemoved(true)
          document.save(tempFile)
          document.close()

          // Replace the original file with the unencrypted one
          if (pdfFile.delete() && tempFile.renameTo(pdfFile)) {
            return@ensureInitialized true
          }
        } else {
          document.close()
          // Document is not encrypted
          return@ensureInitialized true
        }

        return@ensureInitialized false
      } catch (e: Exception) {
        debugLog(
          tag = "PdfRemovePasswordUiModel",
          throwable = e
        ) { "Error removing password: ${e.message}" }
        return@ensureInitialized false
      }
    }
  }
}
