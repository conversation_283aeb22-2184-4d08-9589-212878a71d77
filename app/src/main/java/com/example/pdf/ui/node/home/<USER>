package com.example.pdf.ui.node.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.pdf.R
import com.example.pdf.android.file.DocumentTypeItemData
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Text
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.ui.composable.BlankSpacer

@Composable
fun EmptyFileListPlaceholder(
  documentTypeItemData: DocumentTypeItemData,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Column(
    modifier = modifier,
    verticalArrangement = Arrangement.Center,
    horizontalAlignment = Alignment.CenterHorizontally
  ) {
    Image(
      painter = painterResource(R.drawable.img_empty_file_list_placeholder),
      contentDescription = strings.contentDescriptionNoFiles,
      modifier = Modifier.size(72.dp)
    )

    BlankSpacer(16.dp)

    Text(
      text = "No ${documentTypeItemData.label} files found",
      style = AppTheme.typography.body1,
      fontWeight = FontWeight.Medium,
      color = AppTheme.colors.onDisabled.copy(.8f)
    )

    BlankSpacer(32.dp)
  }
}

@Composable
fun EmptyFileListPlaceholderForBookmarks(
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Column(
    modifier = modifier,
    verticalArrangement = Arrangement.Center,
    horizontalAlignment = Alignment.CenterHorizontally
  ) {
    Image(
      painter = painterResource(R.drawable.img_empty_file_list_placeholder),
      contentDescription = strings.contentDescriptionNoBookmarks,
      modifier = Modifier.size(72.dp)
    )

    BlankSpacer(16.dp)

    Text(
      text = strings.placeholderNoBookmarks,
      style = AppTheme.typography.body1,
      fontWeight = FontWeight.Medium,
      color = AppTheme.colors.onDisabled.copy(.8f)
    )

    BlankSpacer(32.dp)
  }
}