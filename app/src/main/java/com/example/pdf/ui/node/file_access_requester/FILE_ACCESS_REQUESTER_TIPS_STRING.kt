package com.example.pdf.ui.node.file_access_requester

import android.os.Build
import com.example.pdf.lyricist.globalStrings

val FILE_ACCESS_REQUESTER_TIPS_STRING = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
  globalStrings.permissionFileAccessTips
} else {
  globalStrings.permissionFileAccessTipsLegacy
}


val FILE_ACCESS_REQUESTER_TIPS_SHORT_STRING = globalStrings.permissionFileAccessTipsShort
