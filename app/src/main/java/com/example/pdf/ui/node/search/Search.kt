package com.example.pdf.ui.node.search

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.material.icons.filled.Clear
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleStartEffect
import com.example.pdf.PreviewComposable
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.textfield.TextField
import com.example.pdf.lumo.components.textfield.TextFieldDefaults
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.lumo.components.topbar.TopBarDefaults
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.document_item.DocumentItem
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Parcelize
class SearchNode : ScreenNode("search") {
  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    val uiModel: SearchUiModel = koinUiModel()
    val uiState by uiModel.collectAsState()

    SearchContent(
      uiState = uiState,
      onQueryChange = uiModel::onSearch,
      onQueryClear = uiModel::onQueryClear,
      onSearchItemClick = { document ->
        DocumentNode(document)?.let(navigator::push)
      },
      onBack = navigator::pop
    )
  }
}

@Composable
private fun SearchContent(
  uiState: SearchUiState,
  onQueryChange: (TextFieldValue) -> Unit,
  onQueryClear: () -> Unit,
  onSearchItemClick: (File) -> Unit,
  onBack: () -> Unit
) {
  Scaffold(
    topBar = {
      SearchTopBar(
        query = uiState.query,
        onQueryChange = onQueryChange,
        onQueryClear = onQueryClear,
        onBack = onBack
      )
    },
//    bottomBar = {
//      BannerAd(BannerAdPlace.Search, modifier = Modifier.navigationBarsPadding())
//    }
  ) {
    LazyColumn(
      modifier = Modifier
        .fillMaxSize()
        .padding(it)
    ) {
      items(uiState.documentResults) { documentFile ->
        val isBookmarked = uiState.bookmarkedFilePaths.contains(documentFile.absolutePath)

        DocumentItem(
          file = documentFile,
          isShowMoreIcon = false,
          isBookmarked = isBookmarked,
          isShowBookmarkIcon = isBookmarked,
          onClick = { file -> onSearchItemClick(file) },
          modifier = Modifier.animateItem()
        )
      }
    }
  }
}

@Composable
private fun SearchTopBar(
  query: TextFieldValue,
  onQueryChange: (TextFieldValue) -> Unit,
  onQueryClear: () -> Unit,
  onBack: () -> Unit
) {
  TopBar(
    colors = TopBarDefaults.topBarColors(containerColor = AppTheme.colors.surface)
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 8.dp)
    ) {
      IconButton(onClick = onBack, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
      }

      SearchBar(query, onQueryChange, onQueryClear, Modifier.padding(horizontal = 8.dp))
    }
  }
}

@Composable
private fun SearchBar(
  query: TextFieldValue,
  onQueryChange: (TextFieldValue) -> Unit,
  onQueryClear: () -> Unit,
  modifier: Modifier = Modifier,
) {
  val focusRequester = remember { FocusRequester() }
  val keyboardController = LocalSoftwareKeyboardController.current
  val focusManager = LocalFocusManager.current

  LifecycleStartEffect(Unit) {
    focusRequester.requestFocus()

    onStopOrDispose {
      focusManager.clearFocus()
      keyboardController?.hide()
    }
  }

  TextField(
    modifier = modifier.focusRequester(focusRequester),
    value = query,
    onValueChange = onQueryChange,
    singleLine = true,
    shape = CircleShape,
    colors = TextFieldDefaults.colors().copy(
      unfocusedContainerColor = AppTheme.colors.secondary.copy(.25f),
      focusedContainerColor = AppTheme.colors.secondary.copy(.25f),
      cursorColor = AppTheme.colors.primary,
    ),
    placeholder = { Text(LocalStrings.current.placeholderSearch) },
    trailingIcon = {
      if (query.text.isNotEmpty()) {
        IconButton(
          onClick = onQueryClear,
          variant = IconButtonVariant.Secondary,
          shape = CircleShape,
          modifier = Modifier.size(24.dp)
        ) {
          Icon(imageVector = Icons.Filled.Clear)
        }
      }
    },
  )
}


@Preview(
  showSystemUi = true
)
@Composable
private fun SearchContentPreview() {
  PreviewComposable {
    SearchContent(uiState = SearchUiState(), {}, {}, {}, {})
  }
}