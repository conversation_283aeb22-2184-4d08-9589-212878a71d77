package com.example.pdf.biz.rating

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material.icons.rounded.Star
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.pdf.R
import com.example.pdf.android.context.findActivity
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.guia.DialogNode
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Surface
import com.example.pdf.lumo.components.Text
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.composable.noRippleClickable
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.rememberNavigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.replaceLast
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.koin.androidx.compose.koinViewModel

@Parcelize
class RatingGuideDialogNode : DialogNode("rating_guide_dialog") {
  @Composable
  override fun Content(navigator: Navigator, dialog: Dialog?) {
    val viewModel = koinViewModel<RattingGuideDialogViewModel>()
    RatingGuideDialog(navigator)
  }
}

@Composable
private fun RatingGuideDialog(navigator: Navigator) {
  val scope = rememberCoroutineScope()
  val context = LocalContext.current

  Dialog(
    navigator::pop,
    properties = DialogProperties(dismissOnClickOutside = false)
  ) {
    Surface(shape = RoundedCornerShape(22.dp), modifier = Modifier.fillMaxWidth()) {
      Column(horizontalAlignment = Alignment.CenterHorizontally) {
        IconButton(
          navigator::pop,
          modifier = Modifier
            .align(Alignment.End),
          colors = IconButtonDefaults.iconButtonColors(
            contentColor = Color(0x305C5C5C),
          )
        ) {
          Icon(
            imageVector = Icons.Rounded.Close,
            contentDescription = null,
            modifier = Modifier.size(22.dp)
          )
        }

        Image(
          painter = painterResource(R.drawable.img_rating),
          modifier = Modifier.width(72.dp),
          contentDescription = null
        )

        BlankSpacer(16.dp)

        Text(
          text = "Do you like using our app?",
          style = AppTheme.typography.h2.copy(fontWeight = FontWeight.Medium),
          textAlign = TextAlign.Center,
          modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
        )

        BlankSpacer(6.dp)

        Text(
          text = "Let us know with a quick rating!",
          style = AppTheme.typography.body1,
          color = AppTheme.colors.textSecondary,
          textAlign = TextAlign.Center,
          modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
        )

        BlankSpacer(16.dp)

        var selectedStarCount by remember { mutableIntStateOf(0) }
        RatingStar(
          maxStarCount = 5,
          selectedStarCount = selectedStarCount,
          onStarSelect = { ratingStars ->
            selectedStarCount = ratingStars
            scope.launch {
              delay(150)

              if (ratingStars > 4) {
                logEventRecord("click_rate_dialog_review")
                navigator.pop()
                RatingHelper.openGpInAppReviews(context.findActivity())
              } else {
                logEventRecord("click_rate_dialog_feedback")
                navigator.replaceLast(FeedbackGuideDialogNode())
              }
            }
          }
        )

        BlankSpacer(22.dp)

        Text(
          text = LocalStrings.current.skipForNow,
          style = AppTheme.typography.body2,
          color = Color.Gray,
          modifier = Modifier.clickable { navigator.pop() }
        )

        BlankSpacer(16.dp)
      }
    }
  }
}

@Composable
private fun RatingStar(
  maxStarCount: Int,
  selectedStarCount: Int,
  onStarSelect: (selectedStarCount: Int) -> Unit,
  modifier: Modifier = Modifier,
) {
  Row(modifier = modifier) {
    (0 until maxStarCount).forEach { index ->
      val tintColor = if (selectedStarCount < index + 1) Color(0xFFD8D8D8) else Color(0xFFFFCB30)

      Icon(
        imageVector = Icons.Rounded.Star,
        contentDescription = null,
        tint = tintColor,
        modifier = Modifier
          .size(48.dp)
          .noRippleClickable {
            onStarSelect(index + 1)
          }
      )

      if (maxStarCount != index + 1) {
        BlankSpacer(width = 2.dp)
      }
    }
  }
}


@Preview
@Composable
private fun RatingGuideDialogPreview() {
  AppTheme {
    RatingGuideDialog(rememberNavigator())
  }
}