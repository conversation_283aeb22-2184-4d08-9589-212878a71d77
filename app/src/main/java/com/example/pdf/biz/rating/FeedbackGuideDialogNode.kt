package com.example.pdf.biz.rating

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.R
import com.example.pdf.android.context.sendEmail
import com.example.pdf.guia.DialogNode
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Surface
import com.example.pdf.lumo.components.Text
import com.example.pdf.ui.composable.BlankSpacer
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.rememberNavigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize

@Parcelize
class FeedbackGuideDialogNode : DialogNode("feedback_guide_dialog") {
  @Composable
  override fun Content(navigator: Navigator, dialog: Dialog?) {
    FeedbackGuideDialog(navigator)
  }

}

@Composable
private fun FeedbackGuideDialog(navigator: Navigator) {
  val context = LocalContext.current
  val strings = LocalStrings.current

  Dialog(
    navigator::pop,
    properties = DialogProperties(dismissOnClickOutside = false)
  ) {
    Surface(shape = RoundedCornerShape(22.dp), modifier = Modifier.fillMaxWidth()) {
      Column(horizontalAlignment = Alignment.CenterHorizontally) {
        IconButton(
          navigator::pop,
          modifier = Modifier
            .align(Alignment.End),
          colors = IconButtonDefaults.iconButtonColors(
            contentColor = Color(0x305C5C5C),
          )
        ) {
          Icon(
            imageVector = Icons.Rounded.Close,
            contentDescription = null,
            modifier = Modifier.size(22.dp)
          )
        }

        Image(
          painter = painterResource(R.drawable.img_email),
          modifier = Modifier.size(72.dp),
          contentDescription = null
        )

        BlankSpacer(20.dp)

        Text(
          text = strings.feedbackDialogMessage,
          style = AppTheme.typography.h2.copy(fontWeight = FontWeight.Medium),
          textAlign = TextAlign.Center,
          modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
        )

        BlankSpacer(20.dp)

        Button (
          onClick = {
            context.sendEmail("<EMAIL>", subject = "Feedback")
            navigator.pop()
          },
          modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 42.dp),
        ) {
          Text(text = strings.textFeedback, style = AppTheme.typography.buttonLarge)
        }

        BlankSpacer(16.dp)

        Text(
          text = strings.skipForNow,
          style = AppTheme.typography.body2,
          color = Color.Gray,
          modifier = Modifier.clickable { navigator.pop() }
        )

        BlankSpacer(20.dp)
      }
    }
  }
}


@Preview
@Composable
private fun FeedbackGuideDialogPreview() {
  AppTheme {
    FeedbackGuideDialog(rememberNavigator())
  }
}